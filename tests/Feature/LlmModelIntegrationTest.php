<?php

namespace Tests\Feature;

use App\Models\LlmModel;
use App\Services\AI\AIService;
use App\Services\AI\AIServiceFactory;
use App\Services\AI\OllamaService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class LlmModelIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock HTTP responses for Ollama API
        Http::fake([
            '*/api/chat' => Http::response([
                'message' => [
                    'role' => 'assistant',
                    'content' => 'Test response from Ollama'
                ],
                'done' => true
            ], 200),
            '*/api/generate' => Http::response([
                'response' => 'Test completion response',
                'done' => true
            ], 200),
        ]);
    }

    /** @test */
    public function it_can_create_ollama_service_for_llm_model()
    {
        $model = LlmModel::create([
            'name' => 'test-model:latest',
            'display_name' => 'Test Model',
            'has_reasoning' => true,
            'context_window' => 4096,
            'status' => 'active'
        ]);

        $service = AIServiceFactory::createOllamaForModel($model);

        $this->assertInstanceOf(OllamaService::class, $service);
    }

    /** @test */
    public function it_maps_llm_model_properties_to_ollama_options()
    {
        $model = LlmModel::create([
            'name' => 'thinking-model:latest',
            'display_name' => 'Thinking Model',
            'has_reasoning' => true,
            'has_vision' => true,
            'has_function_calling' => true,
            'context_window' => 8192,
            'architecture' => 'llama',
            'format' => 'GGUF',
            'status' => 'active'
        ]);

        $service = new OllamaService([
            'api_url' => 'http://localhost:11434/api',
            'model' => 'default-model'
        ]);

        // Use reflection to test the protected extractModelOptions method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('extractModelOptions');
        $method->setAccessible(true);

        $options = $method->invoke($service, $model);

        // Verify that LLMModel properties are correctly mapped
        $this->assertEquals('thinking-model:latest', $options['model']);
        $this->assertTrue($options['think']);
        $this->assertEquals(8192, $options['num_ctx']);
        $this->assertTrue($options['supports_vision']);
        $this->assertTrue($options['supports_tools']);
        $this->assertEquals('llama', $options['architecture']);
        $this->assertEquals('GGUF', $options['format']);
    }

    /** @test */
    public function it_sends_message_with_model_configuration()
    {
        $model = LlmModel::create([
            'name' => 'reasoning-model:latest',
            'display_name' => 'Reasoning Model',
            'has_reasoning' => true,
            'context_window' => 4096,
            'status' => 'active'
        ]);

        $aiService = AIService::forModelWithOllama($model);
        $response = $aiService->sendMessage('Test prompt');

        $this->assertEquals('Test response from Ollama', $response);

        // Verify that the correct parameters were sent to Ollama
        Http::assertSent(function ($request) {
            $data = json_decode($request->body(), true);

            // Debug: dump the actual request data
            dump('Request data:', $data);

            return $data['model'] === 'reasoning-model:latest' &&
                   isset($data['think']) && $data['think'] === true &&
                   isset($data['options']['num_ctx']) &&
                   $data['options']['num_ctx'] === 4096;
        });
    }

    /** @test */
    public function it_sends_conversation_with_model_configuration()
    {
        $model = LlmModel::create([
            'name' => 'chat-model:latest',
            'display_name' => 'Chat Model',
            'has_reasoning' => false,
            'context_window' => 2048,
            'status' => 'active'
        ]);

        $messages = [
            ['role' => 'user', 'content' => 'Hello'],
            ['role' => 'assistant', 'content' => 'Hi there!'],
            ['role' => 'user', 'content' => 'How are you?']
        ];

        $aiService = AIService::forModelWithOllama($model);
        $response = $aiService->sendConversation($messages);

        $this->assertEquals('Test response from Ollama', $response);

        // Verify that the correct parameters were sent to Ollama
        Http::assertSent(function ($request) {
            $data = json_decode($request->body(), true);
            
            return $data['model'] === 'chat-model:latest' &&
                   !isset($data['think']) && // Should not have think parameter
                   isset($data['options']['num_ctx']) &&
                   $data['options']['num_ctx'] === 2048;
        });
    }

    /** @test */
    public function it_handles_model_without_reasoning_capability()
    {
        $model = LlmModel::create([
            'name' => 'simple-model:latest',
            'display_name' => 'Simple Model',
            'has_reasoning' => false,
            'context_window' => 1024,
            'status' => 'active'
        ]);

        $service = new OllamaService([
            'api_url' => 'http://localhost:11434/api',
            'model' => 'default-model'
        ]);

        // Use reflection to test the protected extractModelOptions method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('extractModelOptions');
        $method->setAccessible(true);

        $options = $method->invoke($service, $model);

        // Verify that think parameter is not set for non-reasoning models
        $this->assertArrayNotHasKey('think', $options);
        $this->assertEquals(1024, $options['num_ctx']);
    }

    /** @test */
    public function it_can_override_model_options_with_additional_parameters()
    {
        $model = LlmModel::create([
            'name' => 'test-model:latest',
            'display_name' => 'Test Model',
            'has_reasoning' => true,
            'context_window' => 4096,
            'status' => 'active'
        ]);

        $aiService = AIService::forModelWithOllama($model);
        
        // Override some model settings
        $response = $aiService->sendMessage('Test prompt', [
            'temperature' => 0.9,
            'num_ctx' => 8192, // Override the model's context window
            'think' => false   // Override the model's reasoning capability
        ]);

        $this->assertEquals('Test response from Ollama', $response);

        // Verify that overrides were applied
        Http::assertSent(function ($request) {
            $data = json_decode($request->body(), true);
            
            return $data['model'] === 'test-model:latest' &&
                   $data['think'] === false && // Overridden
                   $data['options']['temperature'] === 0.9 &&
                   $data['options']['num_ctx'] === 8192; // Overridden
        });
    }
}
